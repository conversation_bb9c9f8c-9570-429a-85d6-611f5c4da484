package client

import (
	"fmt"
	"pxpat-backend/pkg/httpclient"
	"time"

	"pxpat-backend/internal/user-cluster/user-service/dto"
)

// UserServiceClient 用户服务客户端接口
type UserServiceClient interface {
	// GetUserInfo 获取用户信息
	GetUserInfo(userKSUID string) (*dto.SimpleUserModel, error)
	// CheckUserExists 检查用户是否存在
	CheckUserExists(userKSUID string) (bool, error)
	// GetUserFullInfo 获取用户完整信息（包含隐私设置）
	GetUserFullInfo(userKSUID string) (*dto.UserFullInfo, error)
}

// userServiceClient 用户服务客户端实现
type userServiceClient struct {
	httpClient *httpclient.HTTPClient
}

// UserServiceConfig 用户服务客户端配置
type UserServiceConfig struct {
	BaseURL string
	Timeout time.Duration
}

// ========= 本地特有的结构体定义 =========
// 注意：大部分结构体已迁移到user-service的dto包中，这里只保留interaction-service特有的结构体

// NewUserServiceClient 创建用户服务客户端
func NewUserServiceClient(config UserServiceConfig) UserServiceClient {
	return &userServiceClient{
		httpClient: httpclient.NewHTTPClient(httpclient.ClientConfig{
			BaseURL:          config.BaseURL,
			Timeout:          config.Timeout,
			RetryCount:       3,
			RetryWaitTime:    1 * time.Second,
			RetryMaxWaitTime: 5 * time.Second,
		}),
	}
}

// GetUserInfo 获取用户信息
func (c *userServiceClient) GetUserInfo(userKSUID string) (*dto.SimpleUserModel, error) {
	var response struct {
		Code int                 `json:"code"`
		Data dto.SimpleUserModel `json:"data"`
	}

	err := c.httpClient.Get(fmt.Sprintf("/api/v1/users/%s", userKSUID), &response)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}

	if response.Code != 200 {
		return nil, fmt.Errorf("user service returned error code: %d", response.Code)
	}

	return &response.Data, nil
}

// CheckUserExists 检查用户是否存在
func (c *userServiceClient) CheckUserExists(userKSUID string) (bool, error) {
	var response struct {
		Code int `json:"code"`
		Data struct {
			Exists bool `json:"exists"`
		} `json:"data"`
	}

	err := c.httpClient.Get(fmt.Sprintf("/api/v1/users/%s/exists", userKSUID), &response)
	if err != nil {
		return false, fmt.Errorf("failed to check user exists: %w", err)
	}

	if response.Code != 200 {
		return false, fmt.Errorf("user service returned error code: %d", response.Code)
	}

	return response.Data.Exists, nil
}

// GetUserFullInfo 获取用户完整信息（包含隐私设置）
func (c *userServiceClient) GetUserFullInfo(userKSUID string) (*dto.UserFullInfo, error) {
	var response struct {
		Code int              `json:"code"`
		Data dto.UserFullInfo `json:"data"`
	}

	err := c.httpClient.Get(fmt.Sprintf("/api/v1/users/%s/full", userKSUID), &response)
	if err != nil {
		return nil, fmt.Errorf("failed to get user full info: %w", err)
	}

	if response.Code != 200 {
		return nil, fmt.Errorf("user service returned error code: %d", response.Code)
	}

	return &response.Data, nil
}
