package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/video-service/client"
	"pxpat-backend/internal/content-cluster/video-service/dto"
	"pxpat-backend/internal/content-cluster/video-service/model"
	"pxpat-backend/internal/content-cluster/video-service/repository"
	userdto "pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/pkg/errors"
)

// ContentService 内部内容服务
type ContentService struct {
	contentRepo             *repository.ContentRepository
	tagRepo                 *repository.TagRepository
	contentCollaboratorRepo repository.ContentUserRoleRepository
	userServiceClient       client.UserServiceClient
	retryRepo               *repository.UserCreationRetryRepository
	publishStatsRepo        *repository.PublishStatsRepository
}

// NewContentService 创建内部内容服务
func NewContentService(contentRepo *repository.ContentRepository, tagRepo *repository.TagRepository, contentUserRoleRepo repository.ContentUserRoleRepository, userServiceClient client.UserServiceClient, retryRepo *repository.UserCreationRetryRepository, publishStatsRepo *repository.PublishStatsRepository) *ContentService {
	return &ContentService{
		contentRepo:             contentRepo,
		tagRepo:                 tagRepo,
		contentCollaboratorRepo: contentUserRoleRepo,
		userServiceClient:       userServiceClient,
		retryRepo:               retryRepo,
		publishStatsRepo:        publishStatsRepo,
	}
}

// UpdateContentStatusInternal 内部更新内容状态
func (s *ContentService) UpdateContentStatusInternal(ctx context.Context, req *dto.UpdateContentStatusRequest) *errors.Errors {
	log.Info().
		Str("video_ksuid", req.ContentKSUID).
		Str("status", req.Status).
		Msg("开始内部更新内容状态")

	// 验证状态值
	if !isValidContentStatus(req.Status) {
		log.Warn().
			Str("video_ksuid", req.ContentKSUID).
			Str("status", req.Status).
			Msg("无效的内容状态")
		return errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, nil)
	}

	// 更新内容状态
	err := s.contentRepo.UpdateContentStatus(ctx, req.ContentKSUID, model.ContentStatus(req.Status))
	if err != nil {
		log.Error().
			Err(err).
			Str("video_ksuid", req.ContentKSUID).
			Str("status", req.Status).
			Msg("内部更新内容状态失败")
		return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.UPDATE_RECORD_ERROR, err)
	}

	log.Info().
		Str("video_ksuid", req.ContentKSUID).
		Str("status", req.Status).
		Msg("内部更新内容状态成功")

	return nil
}

// GetContentByIdInternal 内部获取内容详情
func (s *ContentService) GetContentByIdInternal(ctx context.Context, contentKSUID string) (*model.Content, *errors.Errors) {
	log.Info().
		Str("content_ksuid", contentKSUID).
		Msg("开始内部获取内容详情")

	content, err := s.contentRepo.GetContentByContentKSUID(ctx, contentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("内部获取内容详情失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	log.Info().
		Str("content_ksuid", contentKSUID).
		Msg("内部获取内容详情成功")

	return content, nil
}

// BatchGetContentsByKSUIDs 批量获取内容信息
func (s *ContentService) BatchGetContentsByKSUIDs(ctx context.Context, contentKSUIDs []string) ([]*dto.ContentSimpleModel, *errors.Errors) {
	log.Info().
		Int("content_count", len(contentKSUIDs)).
		Msg("开始批量获取内容信息")

	if len(contentKSUIDs) == 0 {
		return []*dto.ContentSimpleModel{}, nil
	}

	// 调用 repository 批量获取内容
	contents, err := s.contentRepo.BatchGetContentsByKSUIDs(ctx, contentKSUIDs)
	if err != nil {
		log.Error().
			Err(err).
			Int("content_count", len(contentKSUIDs)).
			Msg("批量获取内容信息失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 转换为 DTO
	var result []*dto.ContentSimpleModel
	for _, content := range contents {
		result = append(result, &dto.ContentSimpleModel{
			UserKSUID:    content.UserKSUID,
			ContentKSUID: content.ContentKSUID,
			Title:        content.Title,
			CoverURL:     content.CoverURL,
			Duration:     content.Duration,
			ViewCount:    content.ViewCount,
			CommentCount: content.CommentCount,
		})
	}

	log.Info().
		Int("requested_count", len(contentKSUIDs)).
		Int("found_count", len(result)).
		Msg("批量获取内容信息成功")

	return result, nil
}

// FinishedAuditInternal 内部完成审核处理
func (s *ContentService) FinishedAuditInternal(ctx context.Context, event *dto.AuditOKEvent) *errors.Errors {
	log.Info().
		Str("video_ksuid", event.ContentKSUID).
		Str("level", event.Level).
		Uint64("audit_task_id", event.AuditTaskID).
		Msg("开始内部完成审核处理")

	// 1. 获取内容详情
	content, err := s.contentRepo.GetContentByContentKSUID(ctx, event.ContentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("video_ksuid", event.ContentKSUID).
			Msg("获取内容详情失败")
		return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 2. 设置内容状态、审核ID和审核级别
	content.AuditTaskID = event.AuditTaskID
	content.Status = "published"
	content.Level = event.Level // 设置审核级别
	err = s.contentRepo.UpdateContent(ctx, content)
	if err != nil {
		log.Error().
			Err(err).
			Uint64("audit_task_id", event.AuditTaskID).
			Str("video_ksuid", event.ContentKSUID).
			Str("level", event.Level).
			Msg("更新内容状态、审核ID和审核级别失败")
		return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.UPDATE_RECORD_ERROR, err)
	}

	// 3. 处理original_tags
	if content.OriginalTags != "" {
		gErr := s.processOriginalTags(ctx, event.ContentKSUID, content.OriginalTags)
		if gErr != nil {
			log.Error().
				Err(gErr).
				Str("video_ksuid", event.ContentKSUID).
				Str("original_tags", content.OriginalTags).
				Msg("处理原始标签失败")
			return gErr
		}
	}

	// 4. 处理主创和成员
	if err := s.processOriginalCollaborators(ctx, content); err != nil {
		log.Error().
			Err(err).
			Str("video_ksuid", event.ContentKSUID).
			Msg("处理主创和成员失败")
		// 不返回错误，因为这不应该阻止审核完成，只记录错误
	}

	// 5. 更新发布视频统计
	if err := s.updatePublishStats(ctx, content.UserKSUID, content.CategoryID); err != nil {
		log.Error().
			Err(err).
			Str("video_ksuid", event.ContentKSUID).
			Str("user_ksuid", content.UserKSUID).
			Uint("category_id", content.CategoryID).
			Msg("更新发布视频统计失败")
		// 不返回错误，因为这不应该阻止审核完成，只记录错误
	}

	log.Info().
		Str("video_ksuid", event.ContentKSUID).
		Str("level", event.Level).
		Uint64("audit_task_id", event.AuditTaskID).
		Msg("内部完成审核处理成功")

	return nil
}

// processOriginalTags 处理原始标签
func (s *ContentService) processOriginalTags(ctx context.Context, videoKSUID, originalTags string) *errors.Errors {
	log.Info().
		Str("video_ksuid", videoKSUID).
		Str("original_tags", originalTags).
		Msg("开始处理原始标签")

	// 解析标签字符串
	tagNames := strings.Split(originalTags, ",")
	var tags []*model.Tag

	for _, tagName := range tagNames {
		tagName = strings.TrimSpace(tagName)
		if tagName == "" {
			continue
		}

		// 获取或创建标签
		tag, err := s.tagRepo.GetOrCreateTagByName(ctx, tagName)
		if err != nil {
			log.Error().
				Err(err).
				Str("video_ksuid", videoKSUID).
				Str("tag_name", tagName).
				Msg("获取或创建标签失败")
			return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.CREATE_RECORD_ERROR, err)
		}

		tags = append(tags, tag)
		log.Debug().
			Str("video_ksuid", videoKSUID).
			Str("tag_name", tagName).
			Uint("tag_id", tag.ID).
			Msg("标签处理成功")
	}

	// 关联内容与标签
	if len(tags) > 0 {
		err := s.contentRepo.AssociateContentWithTags(ctx, videoKSUID, tags)
		if err != nil {
			log.Error().
				Err(err).
				Str("video_ksuid", videoKSUID).
				Int("tag_count", len(tags)).
				Msg("关联内容与标签失败")
			return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.UPDATE_RECORD_ERROR, err)
		}

		log.Info().
			Str("video_ksuid", videoKSUID).
			Int("tag_count", len(tags)).
			Msg("关联内容与标签成功")
	}

	return nil
}

// isValidContentStatus 验证内容状态是否有效
func isValidContentStatus(status string) bool {
	validStatuses := []string{
		"draft",   // 草稿
		"pending", // 待审核
		"audit",   // 审核中
		"transcoding",
		"published", // 已发布
		"rejected",  // 已拒绝
		"deleted",   // 已删除
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// processOriginalCollaborators 处理主创和成员（批量处理版本）
func (s *ContentService) processOriginalCollaborators(ctx context.Context, content *model.Content) error {
	log.Info().
		Str("video_ksuid", content.ContentKSUID).
		Str("共创者", content.OriginalCollaborators).
		Msg("开始批量处理主创和成员")

	var collaborators []dto.CollaboratorBaseInfo
	err := json.Unmarshal([]byte(content.OriginalCollaborators), &collaborators)
	if err != nil {
		return err
	}

	// 批量处理所有人员
	collaborators, err = s.batchProcessCollaborators(ctx, collaborators, content.ContentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("video_ksuid", content.ContentKSUID).
			Msg("批量处理主创和成员失败")
		return fmt.Errorf("批量处理主创和成员失败: %w", err)
	}

	// 创建用户角色关联记录
	err = s.createUserRoleAssociations(ctx, content.ContentKSUID, collaborators)
	if err != nil {
		log.Error().
			Err(err).
			Str("video_ksuid", content.ContentKSUID).
			Msg("创建用户角色关联失败")
		return fmt.Errorf("创建用户角色关联失败: %w", err)
	}

	return nil
}

// batchProcessCollaborators 批量处理人员列表
func (s *ContentService) batchProcessCollaborators(ctx context.Context, collaborators []dto.CollaboratorBaseInfo, videoKSUID string) ([]dto.CollaboratorBaseInfo, error) {
	log.Info().
		Int("collaborator_count", len(collaborators)).
		Str("video_ksuid", videoKSUID).
		Msg("开始批量处理人员")

	// 考虑到在发布时已经把已存在用户关联,且original_collaborators中不包含已存在用户
	// 所以这里只需要批量创建用户即可,单线程不担心并发问题
	collaboratorsCount := len(collaborators)
	if collaboratorsCount > 0 {
		// 1. 快捷创建用户,只需要输入要创建多少个即可
		simpleUserCreateReq := &userdto.SimpleBatchCreateUsersRequest{
			Count: collaboratorsCount,
		}

		simpleUserCreateResp, err := s.userServiceClient.SimpleBatchCreateUsers(simpleUserCreateReq)
		if err != nil {
			log.Error().
				Err(err).
				Int("user_count", collaboratorsCount).
				Msg("批量创建用户失败")
			return nil, err
		} else if simpleUserCreateResp.Total != collaboratorsCount {
			log.Error().
				Int("expected_count", collaboratorsCount).
				Int("actual_count", simpleUserCreateResp.Total).
				Msg("批量创建用户数量不匹配")
			return nil, fmt.Errorf("批量创建用户数量不匹配")
		}
		log.Info().
			Int("success_count", simpleUserCreateResp.Total).
			Msg("批量创建用户完成")

		// 2. 开始创建别名
		var aliasesToCreate []userdto.AliasCreateInfo

		for i := 0; i < simpleUserCreateResp.Total; i++ {
			collaborators[i].UserKSUID = simpleUserCreateResp.UserKSUIDs[i]
			aliasesToCreate = append(aliasesToCreate, userdto.AliasCreateInfo{
				UserKSUID: simpleUserCreateResp.UserKSUIDs[i],
				AliasName: collaborators[i].Name,
			})
		}

		if len(aliasesToCreate) > 0 {
			aliasCreateReq := &userdto.BatchCreateAliasRequest{
				Aliases: aliasesToCreate,
			}

			aliasCreateResp, err := s.userServiceClient.BatchCreateAlias(aliasCreateReq)
			if err != nil {
				log.Error().
					Err(err).
					Int("alias_count", len(aliasesToCreate)).
					Msg("批量创建别名失败")
				// 不返回错误，因为用户已经创建成功
			} else {
				log.Info().
					Int("success_count", aliasCreateResp.SuccessCount).
					Int("failed_count", aliasCreateResp.FailedCount).
					Msg("批量创建别名完成")
			}
		}

	}

	log.Info().
		Str("video_ksuid", videoKSUID).
		Msg("批量处理人员完成")

	return collaborators, nil
}

// createUserRoleAssociations 创建用户角色关联记录
func (s *ContentService) createUserRoleAssociations(ctx context.Context, contentKSUID string, collaborators []dto.CollaboratorBaseInfo) error {
	log.Info().
		Str("video_ksuid", contentKSUID).
		Int("processed_persons_count", len(collaborators)).
		Msg("开始创建用户角色关联记录")

	for _, collaborator := range collaborators {
		// 检查是否已存在关联记录
		existingRole, err := s.contentCollaboratorRepo.GetByContentKSUIDAndUserKSUID(ctx, contentKSUID, collaborator.UserKSUID)
		// 如果查找报错 (当查询err为RecordNotFound时,这里err会为nil)
		if err != nil {
			log.Error().
				Err(err).
				Str("video_ksuid", contentKSUID).
				Str("user_ksuid", collaborator.UserKSUID).
				Msg("根据内容KSUID和用户KSUID获取角色关联失败")
			return err
		}
		if existingRole != nil {
			// 记录已存在，更新角色信息
			log.Info().
				Str("video_ksuid", contentKSUID).
				Str("user_ksuid", collaborator.UserKSUID).
				Msg("用户角色关联已存在，更新角色信息")
			continue
		} else {
			// 创建新的关联记录
			log.Info().
				Str("video_ksuid", contentKSUID).
				Str("user_ksuid", collaborator.UserKSUID).
				Msg("创建新的用户角色关联")

			newCollaboratorRecord := &model.Collaborator{
				ContentKSUID: contentKSUID,
				UserKSUID:    collaborator.UserKSUID,
			}

			// 创建记录
			err = s.contentCollaboratorRepo.Create(ctx, newCollaboratorRecord)
			if err != nil {
				log.Error().
					Err(err).
					Str("video_ksuid", contentKSUID).
					Str("user_ksuid", collaborator.UserKSUID).
					Msg("创建用户角色关联失败")
				return fmt.Errorf("创建用户角色关联失败: %w", err)
			}

			log.Info().
				Str("video_ksuid", contentKSUID).
				Str("user_ksuid", collaborator.UserKSUID).
				Msg("创建用户角色关联成功")
		}
	}

	log.Info().
		Str("video_ksuid", contentKSUID).
		Int("processed_count", len(collaborators)).
		Msg("用户角色关联创建完成")

	return nil
}

func (s *ContentService) Update(ctx context.Context, content *model.Content) error {
	return s.contentRepo.UpdateContent(ctx, content)
}

// updatePublishStats 更新发布视频统计
func (s *ContentService) updatePublishStats(ctx context.Context, userKSUID string, categoryID uint) error {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Uint("category_id", categoryID).
		Msg("开始更新发布视频统计")

	// 容错处理：如果categoryID为0或无效，记录警告但不报错
	if categoryID == 0 {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Uint("category_id", categoryID).
			Msg("分类ID为0，跳过统计更新")
		return nil
	}

	err := s.publishStatsRepo.UpdateCategoryStats(ctx, userKSUID, categoryID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Uint("category_id", categoryID).
			Msg("更新发布视频统计失败")
		return err
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Uint("category_id", categoryID).
		Msg("更新发布视频统计成功")

	return nil
}
